import Link from "next/link";

export default function HomePage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white">
      <div className="container flex flex-col items-center justify-center gap-12 px-4 py-16">
        <h1 className="text-5xl font-extrabold tracking-tight sm:text-[5rem]">
          GSAP <span className="text-[hsl(280,100%,70%)]">Playground</span>
        </h1>
        <div className="flex flex-col items-center gap-2">
          {/* <p className="text-2xl text-white">
              {hello ? hello.greeting : "Loading tRPC query..."}
            </p> */}

          <div className="flex flex-col items-center justify-center gap-4"></div>
          <div className="flex flex-col items-center justify-center gap-5">
            <Link
              href="/scroll"
              className="hover: rounded-full bg-white/10 px-10 py-3 font-bold no-underline transition hover:bg-white/20 hover:text-red-400"
            >
              Scroll Effects
            </Link>
          </div>
          <div className="flex flex-col items-center justify-center gap-5">
            <Link
              href="/printview"
              className="rounded-full bg-white/10 px-10 py-3 font-bold no-underline transition hover:bg-white/20"
            >
              View Resume
            </Link>
          </div>
        </div>
        {/* {session?.user && <LatestPost />} */}
      </div>
    </main>
  );
}
